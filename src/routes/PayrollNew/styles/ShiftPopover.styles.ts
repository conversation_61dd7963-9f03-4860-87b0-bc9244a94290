import Popover from 'react-bootstrap/Popover'

import styled from 'styled-components'
import { theme } from 'styles/theme'

import { AvatarInitials } from 'components/ui/AvatarInitials'
import { OutlineButton } from 'components/ui/OutlineButton'
import CustomSelect from 'components/ui/Select'
import CustomTimePicker from 'components/ui/TimePicker'

import { ReactComponent as CloseIcon } from 'img/icons/closeIcon.svg'
import { ReactComponent as WarningIcon } from 'img/icons/exclamationFilled.svg'
import { ReactComponent as PlusIcon } from 'img/icons/plusIcon.svg'
import { ReactComponent as SearchIcon } from 'img/icons/searchIcon.svg'
import { ReactComponent as DeleteIcon } from 'img/icons/trashNewIcon.svg'

export const PopoverStyled = styled(Popover)`
  width: 21rem;
  max-width: 21rem;
  /* SEVA: REMOVE THIS */
  margin-right: -10rem !important;

  border: 0;
  border-radius: 0.8rem;

  .arrow {
    display: none;
  }
`

export const ContainerStyled = styled.div`
  display: flex;
  flex-direction: column;

  gap: 0.8rem;
  padding: 0.8rem 1.2rem;
`

export const HeaderStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;

  gap: 0.5rem;
`

export const HeaderTitleStyled = styled.p`
  color: ${theme.colorsNew.darkGrey500};
  font-size: 1rem;
  font-family: ${theme.fonts.normal};
`

export const ActivityButtonStyled = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;

  gap: 0.2rem;
  padding: 0.1rem 0.8rem;
  margin-left: auto;

  border: none;
  background: none;

  color: ${theme.colorsNew.darkGrey500};
  font-size: 0.85rem;
  font-family: ${theme.fonts.normal};
  line-height: normal;

  opacity: 0.8;
  :hover,
  :focus {
    opacity: 1;
  }
`

export const SearchIconStyled = styled(SearchIcon)`
  width: 0.75rem;
  height: 0.75rem;
  fill: currentColor;
`

export const CloseButtonStyled = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;

  padding: 0;

  border: none;
  opacity: 0.5;
  background: none;
  img {
    width: 0.9rem;
    height: 0.9rem;
  }
  :hover,
  :focus {
    opacity: 1;
  }
`

export const InfoBlockStyled = styled.div`
  display: flex;
  align-items: center;
  gap: 0.4rem;
`

export const AvatarInitialsStyled = styled(AvatarInitials)`
  width: 1.6rem;
  height: 1.6rem;
  font-size: 0.8rem;
`

export const InfoBlockTextStyled = styled.p`
  color: ${theme.colorsNew.darkGrey500};
  font-size: 0.9rem;
  font-family: ${theme.fonts.normal};
`

export const ShiftListStyled = styled.div`
  display: flex;
  align-items: center;

  border-bottom: 1px solid #d7dfe9;
`

export const ShiftListItemStyled = styled.button<{
  $isActive?: boolean
  $conflictType?: 'orange' | 'red' | 'grey' | 'green'
}>`
  display: flex;
  align-self: stretch;
  align-items: center;
  justify-content: space-between;

  padding: 0.2rem 0.4rem;

  position: relative;

  border: none;
  background: none;

  color: ${({ $isActive, $conflictType }) => {
    // Show conflict colors regardless of active state
    switch ($conflictType) {
      case 'red':
        return '#FF7262'
      case 'orange':
        return '#FF9500'
      case 'grey':
        return theme.colorsNew.midGrey300
      case 'green':
        return theme.colors.green
      default:
        // If active but no conflict, use blue; otherwise use default grey
        return $isActive ? theme.colorsNew.blue : theme.colorsNew.midGrey200
    }
  }};
  font-size: 0.9rem;
  font-family: ${theme.fonts.normal};

  :after {
    content: ${({ $isActive }) => ($isActive ? "''" : '')};
    display: block;
    width: 100%;
    height: 2px;

    position: absolute;
    bottom: -1px;
    left: 0;

    background: ${({ $isActive, $conflictType }) => {
      // Show conflict colors for border regardless of active state
      switch ($conflictType) {
        case 'red':
          return '#FF7262'
        case 'orange':
          return '#FF9500'
        case 'grey':
          return theme.colorsNew.midGrey300
        case 'green':
          return theme.colors.green
        default:
          // If active but no conflict, use blue; otherwise use default grey
          return $isActive ? theme.colorsNew.blue : theme.colorsNew.midGrey200
      }
    }};
  }

  :hover,
  :focus {
    color: ${({ $isActive, $conflictType }) => {
      // Show conflict colors on hover regardless of active state
      switch ($conflictType) {
        case 'red':
          return '#FF7262'
        case 'orange':
          return '#FF9500'
        case 'grey':
          return theme.colorsNew.midGrey300
        case 'green':
          return theme.colors.green
        default:
          // If active but no conflict, use blue; otherwise use default grey
          return $isActive ? theme.colorsNew.blue : theme.colorsNew.midGrey200
      }
    }};

    :after {
      content: '';
    }
  }
`

export const ConflictIndicatorStyled = styled.div<{ $type: 'red' | 'orange' }>`
  display: flex;
  align-items: center;
  justify-content: center;

  width: 1rem;
  height: 1rem;

  color: ${({ $type }) => ($type === 'red' ? '#FF7262' : '#FF9500')};
`

export const PlusIconStyled = styled(PlusIcon)`
  width: 1rem;
  height: 1rem;
  fill: currentColor;
`

export const RoleBlockStyled = styled.div`
  display: flex;
  align-items: center;
  gap: 0.8rem;
`

export const RoleBlockLabelStyled = styled.p`
  color: ${theme.colorsNew.midGrey200};
  font-size: 0.85rem;
  font-family: ${theme.fonts.normal};
`

export const RoleBlockSalaryStyled = styled.div`
  padding: 0.2rem 0.6rem;
  flex-shrink: 0;

  border-radius: 0.8rem;
  background-color: rgba(69, 84, 104, 0.05);

  color: ${theme.colorsNew.darkGrey500};
  font-size: 0.85rem;
  font-family: ${theme.fonts.normal};
`

export const CustomSelectStyled = styled(CustomSelect)<{ $noValue?: boolean }>`
  .Select__control {
    height: 1.8rem;
    min-height: 1.8rem;
    border-color: ${({ $noValue }) => ($noValue ? theme.colorsNew.blue : null)};
    :hover {
      border-color: ${({ $noValue }) =>
        $noValue ? theme.colorsNew.blue : null};
    }
  }
`

export const ShiftBlockStyled = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`

export const RowCellStyled = styled.div<{ $isShortShift?: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;

  padding: 0.1rem 0;
  position: relative;

  color: ${({ $isShortShift }) =>
    $isShortShift ? '#E18700' : theme.colorsNew.darkGrey500};
  font-size: 0.85rem;
  text-align: center;
  font-family: ${theme.fonts.normal};
  line-height: normal;
  :first-of-type {
    justify-content: flex-start;
  }

  :last-of-type {
    justify-content: flex-end;
    padding-left: 0.9rem;
  }
`

export const RowStyled = styled.div`
  display: grid;
  align-items: center;

  gap: 0.5rem;
  grid-template-columns: 1fr 0.7fr 0.7fr 1fr;
  :first-of-type {
    ${RowCellStyled} {
      color: ${theme.colorsNew.midGrey200};
    }
  }
`

export const CustomTimePickerStyled = styled(CustomTimePicker)<{
  $noValue?: boolean
}>`
  height: unset;
  padding: calc(0.1rem - 0.75px) 0.4rem;

  border: 1px solid
    ${({ $noValue }) =>
      $noValue ? theme.colorsNew.blue : 'rgba(164, 170, 185, 0.4)'};
  background: unset;
  border-radius: 0.8rem;

  color: ${theme.colorsNew.darkGrey500};
  font-size: 0.85rem;
  font-family: ${theme.fonts.normal};
  line-height: normal;

  ::placeholder {
    color: ${theme.colorsNew.blue};
  }
`

export const DeleteButtonStyled = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;

  width: 1rem;
  height: 1rem;
  padding: 0;
  margin-left: 0.2rem;

  border: none;
  background-color: transparent;
  color: #afbaca;
  :hover {
    color: ${theme.colors.red};
  }
`

export const DeleteIconStyled = styled(DeleteIcon)`
  width: 0.9rem;
  height: 0.9rem;
  fill: currentColor;
`

export const BreakBlockStyled = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`

export const DividerStyled = styled.div`
  width: 100%;
  height: 1px;
  flex-shrink: 0;
  background-color: #d7dfe9;
`

export const CustomCheckboxStyled = styled.button<{ $isActive?: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;

  width: 1rem;
  height: 1rem;
  padding: 0;
  margin-left: 0.3rem;

  border: 1px solid
    ${({ $isActive }) => ($isActive ? theme.colorsNew.blue : '#d7dfe9')};
  border-radius: 50%;
  background-color: ${({ $isActive }) =>
    $isActive ? '#fff' : 'rgba(28, 34, 43, 0.04)'};

  :after {
    content: ${({ $isActive }) => ($isActive ? "''" : '')};
    display: block;
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 50%;
    background-color: ${theme.colorsNew.blue};
  }

  :hover {
    border-color: ${theme.colorsNew.blue};
    background-color: #fff;
    box-shadow: 0 0 4px 2px rgba(50, 173, 230, 0.2);
  }
`

export const AddBreakButtonStyled = styled.button`
  display: flex;
  align-items: center;
  align-self: flex-end;
  justify-content: center;

  gap: 0.4rem;
  padding: 0.3rem 0;

  border: none;
  background-color: transparent;

  color: ${theme.colorsNew.midGrey200};
  font-size: 0.85rem;
  font-family: ${theme.fonts.normal};
  line-height: normal;

  :hover,
  :focus {
    color: ${theme.colorsNew.blue};
  }
`

export const TotalBlockStyled = styled.div`
  display: flex;
  align-items: center;
  gap: 0.8rem;
`

export const TotalBlockLabelStyled = styled.p`
  color: ${theme.colorsNew.darkGrey500};
  font-size: 0.85rem;
  font-family: ${theme.fonts.normal};
`

export const TotalBlockValueStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;

  flex: 1;
  padding: 0.2rem 0.4rem;

  border-radius: 0.8rem;
  background-color: #e8f6eb;

  color: ${theme.colorsNew.darkGrey500};
  font-size: 0.95rem;
  font-family: ${theme.fonts.bold};
  line-height: normal;
  :first-of-type {
    background-color: #eaeeff;
  }
`

export const WarningButtonStyled = styled.button<{ $isRed?: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;

  padding: 0;

  position: absolute;
  left: 0;

  border: 0;
  background-color: transparent;
  color: ${({ $isRed }) => ($isRed ? '#FF7262' : '#FF9500')};
`

export const WarningIconStyled = styled(WarningIcon)`
  width: 0.8rem;
  height: 0.8rem;
  fill: currentColor;
`

export const TooltipStyled = styled(Popover)<{
  $isRed?: boolean
  $isReclaimTooltip?: boolean
}>`
  min-width: 6rem;
  width: ${({ $isReclaimTooltip }) => ($isReclaimTooltip ? 'unset' : '8.5rem')};
  max-width: ${({ $isReclaimTooltip }) =>
    $isReclaimTooltip ? '12rem' : '8.5rem'};

  padding: ${({ $isReclaimTooltip }) =>
    $isReclaimTooltip ? '0.5rem 1rem' : '0.4rem 1.2rem 0.4rem 0.6rem'};
  margin-top: ${({ $isReclaimTooltip }) =>
    $isReclaimTooltip ? '0.5rem' : '0'};
  margin-bottom: 0.5rem;

  border: 0;
  border-radius: 0.6rem;
  background-color: ${({ $isRed, $isReclaimTooltip }) =>
    $isReclaimTooltip ? '#000' : $isRed ? '#FF7262' : '#fa9702'};

  color: #fff;
  font-size: 0.8rem;
  font-family: ${theme.fonts.regular};
  line-height: normal;

  .arrow {
    left: ${({ $isReclaimTooltip }) =>
      $isReclaimTooltip ? '1rem !important' : null};
    transform: ${({ $isReclaimTooltip }) =>
      $isReclaimTooltip ? 'unset !important' : null};
    :before,
    :after {
      border-top-color: ${({ $isRed, $isReclaimTooltip }) =>
        $isReclaimTooltip ? '#000' : $isRed ? '#FF7262' : '#fa9702'};
    }
  }
`

export const CloseButtonTooltipStyled = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;

  padding: 0;
  position: absolute;
  right: 0.3rem;
  top: 0.3rem;

  border: none;
  background-color: transparent;
`

export const CloseIconStyled = styled(CloseIcon)`
  width: 0.55rem;
  height: 0.55rem;

  stroke: #fff;
`

export const DeleteTooltipButtonStyled = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;

  padding: 0.3rem 0.8rem;
  margin-top: 0.5rem;

  border: 1px solid #fff;
  border-radius: 0.4rem;
  background-color: transparent;

  color: #fff;
  font-size: 0.8rem;
  font-family: ${theme.fonts.normal};
  line-height: normal;

  :hover,
  :focus {
    background-color: rgba(255, 255, 255, 0.1);
  }
`

export const ScrollBlockStyled = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.8rem;

  max-height: 13.6rem;
  overflow-y: auto;
`

export const ButtonBlockStyled = styled.div`
  display: flex;
  align-items: center;
  gap: 0.8rem;
`

export const ButtonWrapStyled = styled.div`
  display: flex;
  flex: 1;
  & + & {
    justify-content: flex-end;
  }
`

export const SaveButtonStyled = styled(OutlineButton)<{
  $isActive?: boolean
  $conflictColor?: 'orange' | 'red' | 'grey' | 'green'
}>`
  width: unset;
  height: 2rem;
  padding: 0.2rem 0.8rem;
  min-width: ${({ $isActive }) => ($isActive ? '3rem' : '6rem')};

  position: relative;

  box-shadow: ${({ $isActive }) => ($isActive ? 'none' : null)};
  border-color: ${({ $isActive, $conflictColor }) => {
    if ($isActive) return '#AFBACA'
    switch ($conflictColor) {
      case 'orange':
        return '#FF9500'
      case 'red':
        return '#FF7262'
      case 'green':
        return '#4BCCAD'
      default:
        return '#AFBACA'
    }
  }};
  border-width: 1px;
  border-radius: 1.2rem;
  background-color: ${({ $conflictColor }) => {
    switch ($conflictColor) {
      case 'orange':
        return '#FF9500'
      case 'red':
        return '#FF7262'
      case 'green':
        return '#4BCCAD'
      default:
        return '#fff'
    }
  }};
  color: ${({ $isActive, $conflictColor }) => {
    if ($isActive) return '#848DA3'
    return $conflictColor === 'orange' ||
      $conflictColor === 'red' ||
      $conflictColor === 'green'
      ? '#fff'
      : '#000'
  }};
  z-index: 2;
  transition:
    min-width 0.3s ease-in-out,
    border-color 1s ease-in-out,
    background-color 0.3s ease-in-out,
    color 0.3s ease-in-out;
  span:first-of-type {
    position: absolute;
    opacity: ${({ $isActive }) => ($isActive ? 0 : 1)};
    transition: opacity 0.5s ease-in-out;
  }
  span:last-of-type {
    opacity: ${({ $isActive }) => ($isActive ? 1 : 0)};
    transition: opacity 0.5s ease-in-out;
  }

  :hover,
  :focus {
    box-shadow: ${({ $isActive }) => ($isActive ? 'none' : null)};
    border-color: ${({ $isActive, $conflictColor }) => {
      if ($isActive) return '#AFBACA'
      switch ($conflictColor) {
        case 'orange':
          return '#FF7A00'
        case 'red':
          return '#FF5A4A'
        case 'green':
          return '#3BA892'
        default:
          return '#4BCCAD'
      }
    }};
    background-color: ${({ $isActive, $conflictColor }) => {
      if ($isActive) return '#fff'
      switch ($conflictColor) {
        case 'orange':
          return '#FF7A00'
        case 'red':
          return '#FF5A4A'
        case 'green':
          return '#3BA892'
        default:
          return '#4BCCAD'
      }
    }};
    color: ${({ $isActive, $conflictColor }) => {
      if ($isActive) return '#848DA3'
      return $conflictColor === 'orange' ||
        $conflictColor === 'red' ||
        $conflictColor === 'green'
        ? '#fff'
        : '#fff'
    }};
  }
`

export const OrangeButtonStyled = styled(OutlineButton)<{
  $isActive?: boolean
}>`
  flex: 1;
  width: unset;
  height: 2rem;
  padding: 0.2rem 0.8rem;

  position: relative;

  border: 0;
  border-radius: 1.2rem;
  background: linear-gradient(#ffa100, #ff4d00); // initial gradient

  color: #fff;
  overflow: hidden;
  pointer-events: ${({ $isActive }) => ($isActive ? 'none' : null)};
  z-index: 2;

  p:first-of-type {
    position: absolute;
    opacity: ${({ $isActive }) => ($isActive ? 0 : 1)};
    transition: opacity 0.3s ease-in-out;
    z-index: 4;
  }

  p:last-of-type {
    position: absolute;
    opacity: ${({ $isActive }) => ($isActive ? 1 : 0)};
    transition: opacity 1s ease-in-out;
    z-index: 4;
  }
  ::after {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(
      90deg,
      #01d9d5 0%,
      #00befa 18.27%,
      #9967ff 42.79%,
      #fd5c61 61.54%,
      #ff902d 84.13%,
      #ffa000 97.12%
    );

    opacity: 0;
    transition: opacity 1s ease-in;
    z-index: 3;
  }
  ::after {
    opacity: ${({ $isActive }) => ($isActive ? 1 : null)};
  }
`

export const GreyButtonStyled = styled(OutlineButton)`
  width: unset;
  height: 2rem;
  padding: 0.2rem 0.8rem;

  border-width: 1px;
  border-radius: 1.2rem;
  border-color: #848da3;
  background-color: #fff;

  color: #848da3;
  font-family: ${theme.fonts.normal};
  line-height: normal;
`

export const DisabledButtonStyled = styled.button`
  width: unset;
  height: 2rem;
  padding: 0.2rem 0.8rem;
  min-width: 6rem;

  border-width: 1px;
  border-radius: 1.2rem;
  border-color: #d1d5db;
  background-color: #f3f4f6;

  color: #9ca3af;
  font-family: ${theme.fonts.normal};
  font-size: 0.875rem;
  line-height: normal;
  cursor: not-allowed;

  :hover,
  :focus {
    border-color: #d1d5db;
    background-color: #f3f4f6;
    color: #9ca3af;
  }
`

// New styled components for inline styles replacement

export const ErrorContainerStyled = styled.div`
  padding: 1rem;
  text-align: center;
`

export const ErrorTextStyled = styled.p`
  margin: 0;
`

export const ErrorDetailsStyled = styled.p`
  font-size: 0.8rem;
  color: #666;
  margin-top: 0.5rem;
  margin-bottom: 0;
`

export const UnsavedChangesIndicatorStyled = styled.span`
  color: #ff9500;
  margin-left: 8px;
  font-size: 0.8em;
`

export const EmployeeIdStyled = styled.span`
  font-size: 0.8rem;
  color: #666;
  margin-left: 0.5rem;
`

export const ShiftTabContentStyled = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
`

export const YearlySalaryDetailsStyled = styled.div`
  font-size: 0.75rem;
  color: #666;
  margin-top: 2px;
`

export const BonusSalaryStyled = styled.div`
  font-size: 0.75rem;
  background-color: #a4cbb0;
  margin-top: 2px;
  padding: 2px 4px;
  border-radius: 4px;
`

export const NoBreaksMessageStyled = styled(RowCellStyled)`
  text-align: center;
  font-style: italic;
  color: #666;
  padding: 20px;
  grid-column: 1 / -1;
`
