import React, { forwardRef, useEffect, useMemo, useRef, useState } from 'react'
import Overlay from 'react-bootstrap/Overlay'
import OverlayTrigger from 'react-bootstrap/OverlayTrigger'
import { useSelector } from 'react-redux'
import { I18n } from 'react-redux-i18n'
import { toast } from 'react-toastify'

import dayjs from 'dayjs'
import moment, { Moment } from 'moment'
import { RootState } from 'store/reducers'

import NumberFormatted from 'components/ui/NumberFormatted'

import ActivitiesPopover from '../ActivitiesPopover'
import DeleteShiftModal from '../DeleteShiftModal'
import PayrollToaster from '../PayrollToaster'

import {
  PopoverStyled,
  ContainerStyled,
  HeaderStyled,
  HeaderTitleStyled,
  ActivityButtonStyled,
  SearchIconStyled,
  CloseButtonStyled,
  InfoBlockStyled,
  AvatarInitialsStyled,
  InfoBlockTextStyled,
  ShiftListStyled,
  ShiftListItemStyled,
  // ConflictIndicatorStyled, // Unused
  PlusIconStyled,
  RoleBlockStyled,
  RoleBlockLabelStyled,
  RoleBlockSalaryStyled,
  CustomSelectStyled,
  ShiftBlockStyled,
  RowCellStyled,
  RowStyled,
  CustomTimePickerStyled,
  DeleteButtonStyled,
  DeleteIconStyled,
  BreakBlockStyled,
  DividerStyled,
  CustomCheckboxStyled,
  AddBreakButtonStyled,
  TotalBlockStyled,
  TotalBlockLabelStyled,
  TotalBlockValueStyled,
  WarningButtonStyled,
  WarningIconStyled,
  TooltipStyled,
  CloseButtonTooltipStyled,
  CloseIconStyled,
  DeleteTooltipButtonStyled,
  ScrollBlockStyled,
  ButtonBlockStyled,
  ButtonWrapStyled,
  SaveButtonStyled,
  OrangeButtonStyled,
  GreyButtonStyled,
  // DisabledButtonStyled // Unused
  // New styled components for inline styles replacement
  ErrorContainerStyled,
  ErrorTextStyled,
  ErrorDetailsStyled,
  UnsavedChangesIndicatorStyled,
  EmployeeIdStyled,
  ShiftTabContentStyled,
  YearlySalaryDetailsStyled,
  BonusSalaryStyled
} from '../../../styles/ShiftPopover.styles'

import { AttendanceShift, AttendanceShifts } from 'types/attendance'
import { Company } from 'types/company'
import { IEmployee } from 'types/employee'
import { Shift } from 'types/schedule'

import { getEmployeeRate } from 'utils/employees'
import { checkShiftOverlap } from 'utils/attendance'

import {
  analyzeShiftStatus,
  getShiftStatusColor,
  type ShiftAnalysis
} from 'utils/payroll/shiftStatusAnalysis'

import closeIcon from 'img/icons/closeIcon.svg'

// Helper function to convert ShiftAnalysis to button states (replacing getPopoverButtonStates)
const getButtonStatesFromShiftAnalysis = (analysis: ShiftAnalysis) => {
  const hasRedConflicts = analysis.status === 'overlapping'
  const hasGreyConflicts = analysis.status === 'other-role'
  const hasOrangeIssues = analysis.status === 'problematic'

  return {
    canSave: !hasRedConflicts && !hasGreyConflicts,
    canApprove: !hasRedConflicts && !hasGreyConflicts,
    showReclaim: hasOrangeIssues,
    saveButtonColor: analysis.status === 'approved' ? 'green' : 'orange',
    approveButtonColor: analysis.status === 'approved' ? 'green' : 'orange'
  }
}

// Helper function to convert color string to expected styled component type
const mapColorToConflictType = (colorString: string | undefined): 'green' | 'orange' | 'grey' | 'red' | undefined => {
  switch (colorString) {
    case 'blue':
      return undefined // Blue is handled differently in ShiftPopover (no conflict type for current shifts)
    case 'white':
      return 'green' // Map white (approved) to green
    case 'grey':
      return 'grey'
    case 'orange':
      return 'orange'
    case 'red':
      return 'red'
    default:
      return undefined
  }
}

// Extended shift type for enhanced properties
type ExtendedAttendanceShift = AttendanceShift & {
  isStartOverlapping?: boolean
  isEndOverlapping?: boolean
  scheduledShift?: Shift
}

// TODO SEVA - once ref is properly send => check TimePicker overflow
type ShiftPopoverProps = {
  onClose: () => void
  showActivityPopoverOnLeft?: boolean
  employee?: IEmployee
  date?: string
  shifts?: AttendanceShift[]
  isNewShift?: boolean
  currentCompany?: Company
  attendanceData?: AttendanceShifts
  onSave?: (newShift: { [key: string]: AttendanceShift }) => void
  onDeleteShift?: (shiftKey: string) => void
}

const ShiftPopover = forwardRef<HTMLDivElement, ShiftPopoverProps>(
  (
    {
      onClose,
      showActivityPopoverOnLeft,
      employee,
      date,
      shifts = [],
      isNewShift = false,
      currentCompany,
      attendanceData,
      onSave,
      onDeleteShift,
      ...rest
    },
    ref
  ) => {
    const [activeShift, setActiveShift] = useState<number | null>(0)
    
    // Local state to manage all shift edits without immediately saving to Firebase
    const [localShifts, setLocalShifts] = useState<AttendanceShift[]>(shifts)
    
    // Track unsaved changes per shift
    const [unsavedChanges, setUnsavedChanges] = useState<{[shiftIndex: number]: Partial<AttendanceShift>}>({})

    const isLocaleFr =
      useSelector((state: RootState) => state.i18n.locale) === 'fr'

    // Generate position options matching the exact logic from EmployeeRolesList.js
    const options = React.useMemo(() => {
      if (!currentCompany?.jobs || !employee?.positions) {
        return []
      }

      const positionOptions: Array<{ label: string; value: string }> = []

      // Clone and sort jobs by priority (matching EmployeeRolesList.js logic)
      const jobsCopy = { ...currentCompany.jobs }
      const sortedJobs = Object.entries(jobsCopy)
        .filter(([_, value]) => value && !value.archived)
        .map(([key, value]) => ({ ...value, key }))
        .sort((a, b) => a.priority - b.priority)

      // Process each job category
      sortedJobs.forEach((category) => {
        // Check if this employee has this category assigned
        const hasCategory = employee.positions?.some(p => p.categoryId === category.key) || false

        if (hasCategory) {
          // Check if employee has any subcategories for this category
          const employeeSubcategories = employee.positions?.filter(p =>
            p.categoryId === category.key && p.subcategoryId
          ) || []

          if (employeeSubcategories.length > 0) {
            // Employee has specific subcategories - only show those subcategories
            if (category.subcategories) {
              const subcategories = Object.entries(category.subcategories)
                .filter(([_, value]) => value && !value.archived)
                .map(([key, value]) => ({ ...value, key }))
                .sort((a, b) => a.priority - b.priority)

              subcategories.forEach((subcategory) => {
                // Check if this employee has this specific subcategory assigned
                const hasSubcategory = employee.positions?.some(p => p.subcategoryId === subcategory.key) || false

                if (hasSubcategory) {
                  positionOptions.push({
                    label: `${category.name} - ${subcategory.name}`,
                    value: subcategory.key
                  })
                }
              })
            }
          } else {
            // Employee only has the main category (no specific subcategories)
            positionOptions.push({
              label: category.name,
              value: category.key
            })
          }
        }
      })

      return positionOptions
    }, [currentCompany?.jobs, employee?.positions])

    const [selectedRole, setSelectedRole] = useState<string | null>(null)

    const [isUnpaidBreak, setIsUnpaidBreak] = useState(false)

    const [roundedTimeStart, setRoundedTimeStart] = useState<Moment | null>(
      null
    )
    const [roundedTimeEnd, setRoundedTimeEnd] = useState<Moment | null>(null)

    const [breakTimeStart, setBreakTimeStart] = useState<Moment | null>(null)
    const [breakTimeEnd, setBreakTimeEnd] = useState<Moment | null>(null)

    // Conflict analysis state - only use hoursTableAnalysis for consistency
  const [hoursTableAnalysis, setHoursTableAnalysis] = useState<ShiftAnalysis | null>(null)
    const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)

    // Separate state for each tooltip
    const [showPlannedTooltip, setShowPlannedTooltip] = useState(true)
    const [showClockedTooltip, setShowClockedTooltip] = useState(false)
    // Removed showShortShiftTooltip - shift length validation disabled
    const [showOverlapTooltip, setShowOverlapTooltip] = useState(false)
    const [showDeleteBreakTooltip, setShowDeleteBreakTooltip] = useState(false)
    const [deleteBreakId, setDeleteBreakId] = useState<string | null>(null)
    const [deleteBreakButtonRef, setDeleteBreakButtonRef] = useState<HTMLButtonElement | null>(null)
    const [canShowTooltip, setCanShowTooltip] = useState(false)

    // Separate refs for each tooltip
    const plannedTooltipRef = useRef<HTMLButtonElement>(null)
    const clockedTooltipRef = useRef<HTMLButtonElement>(null)
   // const shortShiftTooltipRef = useRef<HTMLButtonElement>(null)
    const overlapTooltipRef = useRef<HTMLButtonElement>(null)

    //
    const [showActivityPopover, setShowActivityPopover] = useState(false)

    // Sync props with local state when shifts change from parent
    useEffect(() => {
      setLocalShifts(shifts)
      setUnsavedChanges({}) // Clear unsaved changes when props update
    }, [shifts])

    // Helper function to update local shift data without saving to Firebase
    const updateLocalShift = React.useCallback((shiftIndex: number, updates: Partial<AttendanceShift>) => {
      // Check if any values actually changed to prevent unnecessary state updates
      const currentShift = localShifts[shiftIndex]
      const currentUnsaved = unsavedChanges[shiftIndex] || {}
      const merged = { ...currentShift, ...currentUnsaved }
      
      let hasChanges = false
      for (const [key, newValue] of Object.entries(updates)) {
        if (key === 'breaks') {
          // Deep comparison for breaks object
          const currentBreaks = merged[key] || {}
          const newBreaks = newValue || {}
          if (JSON.stringify(currentBreaks) !== JSON.stringify(newBreaks)) {
            hasChanges = true
            break
          }
        } else if (merged[key as keyof AttendanceShift] !== newValue) {
          hasChanges = true
          break
        }
      }
      
      // Only update state if there are actual changes
      if (hasChanges) {
        setLocalShifts(prev => prev.map((shift, index) => 
          index === shiftIndex ? { ...shift, ...updates } : shift
        ))
        
        setUnsavedChanges(prev => ({
          ...prev,
          [shiftIndex]: { ...prev[shiftIndex], ...updates }
        }))
      }
    }, [localShifts, unsavedChanges])

    // Helper function to save all local changes to Firebase
    const saveLocalChanges = async () => {
      if (!onSave || !employee || !date) return

      try {
        const shiftsToSave: { [key: string]: AttendanceShift } = {}
        
        Object.entries(unsavedChanges).forEach(([shiftIndexStr, changes]) => {
          const shiftIndex = parseInt(shiftIndexStr)
          const shift = localShifts[shiftIndex]
          if (shift) {
            const shiftKey = (shift as any)?.shiftKey || `shift_${Date.now()}_${shiftIndex}`
            shiftsToSave[shiftKey] = { ...shift, ...changes }
          }
        })

        if (Object.keys(shiftsToSave).length > 0) {
          await onSave(shiftsToSave)
          setUnsavedChanges({}) // Clear unsaved changes after successful save
        }
      } catch (error) {
        console.error('Failed to save local changes:', error)
        throw error
      }
    }

    // Get current shift data or create new shift template
    const currentShift: ExtendedAttendanceShift | null = useMemo(() => {
      if (activeShift === null) return null

      // If activeShift index exists in localShifts array, return existing shift with unsaved changes
      if (localShifts && activeShift < localShifts.length) {
        const baseShift = localShifts[activeShift]
        const changes = unsavedChanges[activeShift] || {}
        return { ...baseShift, ...changes } as ExtendedAttendanceShift
      }

      // If activeShift equals localShifts.length, we're creating a new shift
      if (activeShift === localShifts.length) {
        return {
          start: undefined, // No default values - use placeholders in UI
          end: undefined,
          breaks: {},
          positionId: selectedRole || Object.keys(currentCompany?.jobs || {})[0] || '',
          isConfirmed: false,
          manuallyCreated: true
        } as ExtendedAttendanceShift
      }

      return null
    }, [activeShift, localShifts, unsavedChanges, selectedRole, currentCompany?.jobs])

    // Determine shift status based on real data
    const hasWarning = isNewShift
      ? false
      : currentShift
        ? !currentShift.positionId
        : false
    // Check if employee clocked in but didn't clock out using attendance data
    const notClockedOut = React.useMemo(() => {
      if (!attendanceData || !date || !employee || !currentShift) return false

      const dayAttendance = attendanceData[date]
      if (!dayAttendance) return false

      const employeeAttendance = dayAttendance[employee.uid]
      if (!employeeAttendance) return false

      const currentShiftKey = (currentShift as any)?.shiftKey
      if (!currentShiftKey) return false

      const attendanceShift = employeeAttendance[currentShiftKey]
      if (!attendanceShift) return false

      // Employee clocked in but didn't clock out
      return attendanceShift.start && !attendanceShift.end
    }, [attendanceData, date, employee, currentShift])

    // Remove shift duration validation - managers can book any length shifts
    //const isShortShift = false // Disabled as per requirement

    // Use proper checkShiftOverlap function directly (same as PayrollOld)
    const isOverlap = React.useMemo(() => {
      // Don't check overlaps for new shifts (they don't exist in attendance data yet)
      if (isNewShift || !currentShift || !attendanceData || !date || !employee) return false

      // Get all shifts for this employee on this date
      const dayAttendance = attendanceData[date]
      if (!dayAttendance) return false

      const employeeAttendance = dayAttendance[employee.uid]
      if (!employeeAttendance) return false

      // Convert attendance data to format expected by checkShiftOverlap
      const employeeShifts: { [key: string]: AttendanceShift } = {}
      Object.entries(employeeAttendance).forEach(([shiftKey, shift]) => {
        if (shift && typeof shift === 'object' && 'start' in shift) {
          employeeShifts[shiftKey] = shift as AttendanceShift
        }
      })

      // Only check for overlaps if there are multiple shifts
      if (Object.keys(employeeShifts).length < 2) return false

      // Get previous and next day shifts for cross-day overlap detection
      const previousDate = dayjs(date).subtract(1, 'day').format('YYYY-MM-DD')
      const nextDate = dayjs(date).add(1, 'day').format('YYYY-MM-DD')

      const previousDayShifts: { [key: string]: AttendanceShift } = {}
      const nextDayShifts: { [key: string]: AttendanceShift } = {}

      // Get previous day shifts
      const prevDayAttendance = attendanceData[previousDate]?.[employee.uid]
      if (prevDayAttendance) {
        Object.entries(prevDayAttendance).forEach(([shiftKey, shift]) => {
          if (shift && typeof shift === 'object' && 'start' in shift) {
            previousDayShifts[shiftKey] = shift as AttendanceShift
          }
        })
      }

      // Get next day shifts
      const nextDayAttendance = attendanceData[nextDate]?.[employee.uid]
      if (nextDayAttendance) {
        Object.entries(nextDayAttendance).forEach(([shiftKey, shift]) => {
          if (shift && typeof shift === 'object' && 'start' in shift) {
            nextDayShifts[shiftKey] = shift as AttendanceShift
          }
        })
      }



      // Use the same overlap detection as PayrollOld
      const overlappedShifts = checkShiftOverlap({
        employeeShifts,
        previousDayShifts,
        nextDayShifts
      })

      // Check if current shift has overlaps
      const currentShiftKey = (currentShift as any)?.shiftKey
      if (currentShiftKey && overlappedShifts[currentShiftKey]) {
        const shiftOverlap = overlappedShifts[currentShiftKey]
        return shiftOverlap.isStartOverlapping || shiftOverlap.isEndOverlapping
      }

      return false
    }, [isNewShift, currentShift, attendanceData, date, employee])


    // Initialize selectedRole from current shift data
    useEffect(() => {
      if (currentShift?.positionId) {
        setSelectedRole(currentShift.positionId)
      }
    }, [currentShift?.positionId])

    // Initialize time pickers from base shift data (not currentShift to avoid circular dependencies)
    useEffect(() => {
      // Only update time picker state when activeShift changes, not when local changes occur
      const baseShift = activeShift !== null && localShifts[activeShift] ? localShifts[activeShift] : null
      
      if (baseShift?.start) {
        const startMoment = moment()
          .startOf('day')
          .add(baseShift.start, 'minutes')
        setRoundedTimeStart(startMoment)
      } else {
        // Clear time picker if no start time
        setRoundedTimeStart(null)
      }

      if (baseShift?.end) {
        const endMoment = moment()
          .startOf('day')
          .add(baseShift.end, 'minutes')
        setRoundedTimeEnd(endMoment)
      } else {
        // Clear time picker if no end time
        setRoundedTimeEnd(null)
      }
    }, [activeShift, localShifts]) // Only depend on activeShift and base localShifts, not currentShift

    // Initialize break time pickers and unpaid break state from current shift data
    useEffect(() => {
      // Only update break UI state when activeShift changes, not when local changes occur
      const baseShift = activeShift !== null && localShifts[activeShift] ? localShifts[activeShift] : null

      if (baseShift?.breaks) {
        const breakEntries = Object.entries(baseShift.breaks)
        if (breakEntries.length > 0) {
          // Get the first break for the time pickers
          const [, firstBreak] = breakEntries[0]

          // Set break time pickers
          if (firstBreak.start) {
            const breakStartMoment = moment()
              .startOf('day')
              .add(firstBreak.start, 'minutes')
            setBreakTimeStart(breakStartMoment)
          } else {
            setBreakTimeStart(null)
          }

          if (firstBreak.end) {
            const breakEndMoment = moment()
              .startOf('day')
              .add(firstBreak.end, 'minutes')
            setBreakTimeEnd(breakEndMoment)
          } else {
            setBreakTimeEnd(null)
          }

          // Set unpaid break state from the first break's isUnpaid property
          setIsUnpaidBreak((firstBreak as any).isUnpaid || false)
        } else {
          // No breaks exist, clear break time pickers and reset unpaid state
          setBreakTimeStart(null)
          setBreakTimeEnd(null)
          setIsUnpaidBreak(false)
        }
      } else {
        // No breaks data, clear everything
        setBreakTimeStart(null)
        setBreakTimeEnd(null)
        setIsUnpaidBreak(false)
      }
    }, [activeShift, localShifts]) // Only depend on activeShift and localShifts from props

    // Analyze shift for conflicts when shift data changes
    useEffect(() => {
      if (currentShift && employee && date && attendanceData && currentCompany) {
        // For analysis consistency, use the original shift data from props instead of the modified currentShift
        // This ensures the same warnings shown in HoursTable are shown in ShiftPopover
        const originalShift = activeShift !== null && localShifts[activeShift] ? localShifts[activeShift] : currentShift

        // Use only analyzeShiftStatus for consistency with HoursTable
        const analysis = analyzeShiftStatus(
          originalShift,
          employee.uid,
          date,
          attendanceData,
          currentCompany,
          dayjs(date).isSame(dayjs(), 'day')
        )
        setHoursTableAnalysis(analysis)

        // Show tooltip if analysis indicates there are issues
        if (analysis.issues.length > 0) {
          const timer = setTimeout(() => {
            setCanShowTooltip(true)
            setShowPlannedTooltip(true) // Show the main conflict tooltip
          }, 100)
          return () => clearTimeout(timer)
        }
      }
    }, [currentShift, activeShift, shifts, employee, date, attendanceData, currentCompany, hasUnsavedChanges, localShifts])

    // Delay showing tooltip to allow parent popover to position itself
    useEffect(() => {
      if (hasWarning) {
        const timer = setTimeout(() => {
          setCanShowTooltip(true)
        }, 100) // Small delay to ensure parent popover is positioned

        return () => clearTimeout(timer)
      }
    }, [hasWarning])

    const handleClosePopover = () => {
      onClose()
      setShowPlannedTooltip(false)
      setShowClockedTooltip(false)
      // Removed setShowShortShiftTooltip - shift length validation disabled
      setShowOverlapTooltip(false)
    }

    const activityPopoverRef = useRef<HTMLDivElement>(null)
    const [showDeleteShiftModal, setShowDeleteShiftModal] = useState(false)

    const canReclaim = !isNewShift && currentShift && currentShift.positionId
    const saveAmount = 5.4
    const [hasClaimed, setHasClaimed] = useState(false)

    const onChangeStatus = async (
      status: 'approve' | 'delete' | 'claim' | 'modify',
      onUndo?: () => void
    ) => {

      if (!currentShift || !onSave || !employee || !date) {
        toast.error(I18n.t('payroll.missing_required_data_update_shift'))
        return
      }

      try {
        if (status === 'approve') {
          // Mark shift as confirmed/approved
          const approvedShift = {
            ...currentShift,
            isConfirmed: true,
            positionId: selectedRole || currentShift.positionId,
            // Update break times from UI if they were modified
            breaks: breakTimeStart && breakTimeEnd ? {
              'break1': {
                start: breakTimeStart.hour() * 60 + breakTimeStart.minute(),
                end: breakTimeEnd.hour() * 60 + breakTimeEnd.minute(),
                lengthRounded: Math.max(0,
                  (breakTimeEnd.hour() * 60 + breakTimeEnd.minute()) -
                  (breakTimeStart.hour() * 60 + breakTimeStart.minute())
                )
              }
            } : currentShift.breaks
          }

          // For existing shifts, use the shiftKey from the shifts array
          // For new shifts, generate a new key
          let shiftKey: string
          if (isNewShift) {
            shiftKey = `shift_${Date.now()}`
          } else {
            // Get the shiftKey from the shifts array using the activeShift index
            if (activeShift === null || activeShift >= shifts.length) {
              console.error('Invalid activeShift index:', { activeShift, shiftsLength: shifts.length })
              toast.error(I18n.t('payroll.unable_to_save_shift_invalid_index'))
              return
            }

            const shiftWithKey = shifts[activeShift]
            shiftKey = (shiftWithKey as any)?.shiftKey

            if (!shiftKey) {
              console.error('No shiftKey found for existing shift:', { activeShift, shift: shiftWithKey })
              toast.error(I18n.t('payroll.unable_to_save_shift_missing_key'))
              return
            }
          }

          await onSave({ [shiftKey]: approvedShift })
        }

        // Show success toast
        toast(
          <PayrollToaster
            onUndo={onUndo}
            status={status}
          />,
          {
            className: 'payroll-toaster',
            closeButton: false,
            hideProgressBar: true,
            position: 'top-right',
            autoClose: 5000,
            pauseOnFocusLoss: false
          }
        )
      } catch (error) {
        toast.error(I18n.t('payroll.failed_to_update_shift_status'))
        console.error('Error updating shift status:', error)
      }
    }

    // Delete current shift from Firebase and update shift list
    const handleDeleteShift = async () => {
      if (!currentShift || !onDeleteShift || !employee || !date) {
        toast.error(I18n.t('payroll.unable_to_delete_shift_missing_data'))
        return
      }

      try {
        const shiftKey = (currentShift as any).shiftKey
        if (!shiftKey) {
          toast.error(I18n.t('payroll.unable_to_delete_shift_no_key'))
          return
        }

        // Call the delete function passed from parent
        await onDeleteShift(shiftKey)

        // Show success message
        toast.success(I18n.t('payroll.shift_deleted_successfully'))

        // Close the popover if no more shifts exist, or switch to another shift
        if (shifts.length <= 1) {
          onClose()
        } else {
          // Switch to the first available shift after deletion
          setActiveShift(0)
        }
      } catch (error) {
        toast.error(I18n.t('payroll.failed_to_delete_shift'))
        console.error('Error deleting shift:', error)
      }
    }

    // Get employee rate based on selected role
    // Note: Employee rates are stored separately in EmployeeRates/{companyId}/{employeeId}/{positionId}
    // For now, we'll use the position's default rate from company jobs
    const { rate, type, additionalSalary } = getEmployeeRate({
      employeeRate: {}, // Would need to fetch from EmployeeRates in real implementation
      positionId: selectedRole || '',
      jobs: currentCompany?.jobs || {}
    })

    const numericRate = Number(rate) || 0
    const formattedSalary = isLocaleFr
      ? numericRate.toLocaleString('fr-FR', {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        })
      : numericRate.toLocaleString('en-US', {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        })

    // Display rate type and bonus information
    const hasBonus = additionalSalary && additionalSalary > 0
    const bonusAmount = Number(additionalSalary) || 0

    // Handle case when employee data is not available
    // TODO Lie - udate with proper styles, translations if this block is needed
    if (!employee || !date) {
      return (
        <PopoverStyled
          {...rest}
          id='payroll_shift-popover'
          ref={ref}
        >
          <ContainerStyled>
            <HeaderStyled>
              <HeaderTitleStyled>{I18n.t('payroll.shift_details')}</HeaderTitleStyled>
              <CloseButtonStyled onClick={handleClosePopover}>
                <img
                  src={closeIcon}
                  alt=''
                />
              </CloseButtonStyled>
            </HeaderStyled>
            <ErrorContainerStyled>
              <ErrorTextStyled>{I18n.t('payroll.unable_to_load_shift_data')}</ErrorTextStyled>
              <ErrorDetailsStyled>
                {I18n.t('payroll.employee_label')}:{' '}
                {employee
                  ? `${employee.name} ${employee.surname}`
                  : I18n.t('payroll.not_found')}
              </ErrorDetailsStyled>
              <ErrorDetailsStyled>
                {I18n.t('payroll.date_label')}: {date || I18n.t('payroll.not_provided')}
              </ErrorDetailsStyled>
            </ErrorContainerStyled>
          </ContainerStyled>
        </PopoverStyled>
      )
    }
    return (
      <>
        <PopoverStyled
          {...rest}
          id='payroll_shift-popover'
          ref={ref}
        >
          {showDeleteShiftModal && (
            <DeleteShiftModal
              onClose={() => setShowDeleteShiftModal(false)}
              onDelete={() => {
                onChangeStatus('delete')
                setShowDeleteShiftModal(false)
                onClose()
              }}
            />
          )}
          <ContainerStyled ref={activityPopoverRef}>
            <HeaderStyled>
              <HeaderTitleStyled>
                {dayjs(date).format('dddd D')}
                {/* Show indicator if there are unsaved changes */}
                {Object.keys(unsavedChanges).length > 0 && (
                  <UnsavedChangesIndicatorStyled>
                    • {I18n.t('payroll.unsaved_changes')}
                  </UnsavedChangesIndicatorStyled>
                )}
              </HeaderTitleStyled>
              <ActivityButtonStyled
                onClick={() => setShowActivityPopover(!showActivityPopover)}
              >
                <SearchIconStyled />
                {I18n.t('payroll.activities')}
              </ActivityButtonStyled>
              <CloseButtonStyled onClick={handleClosePopover}>
                <img
                  src={closeIcon}
                  alt=''
                />
              </CloseButtonStyled>
            </HeaderStyled>
            <InfoBlockStyled>
              <AvatarInitialsStyled employee={employee} />
              <InfoBlockTextStyled>
                {employee.name} {employee.surname}
                <EmployeeIdStyled>
                  #{employee?.payrollId || employee?.customId || I18n.t('common.not_available')}
                </EmployeeIdStyled>
              </InfoBlockTextStyled>
            </InfoBlockStyled>
            <ShiftListStyled>
              {/* Render existing shifts (Shift 1, Shift 2, etc.) */}
              {localShifts.map((shift, index) => {
                // Get the shift with any local changes applied for display purposes
                const shiftWithChanges = unsavedChanges[index] ? { ...shift, ...unsavedChanges[index] } : shift

                // Analyze each shift for conflicts to show indicators - use original shift for consistency with HoursTable
                const shiftAnalysisForTab = employee && date && attendanceData && currentCompany
                  ? analyzeShiftStatus(
                      shift, // Use original shift data, not shiftWithChanges
                      employee.uid,
                      date,
                      attendanceData,
                      currentCompany,
                      dayjs(date).isSame(dayjs(), 'day')
                    )
                  : null

                // Apply status color based on shift analysis - use the same function as HoursTable
                const statusColor = shiftAnalysisForTab ? getShiftStatusColor(shiftAnalysisForTab.status) : undefined
                let conflictTypeForDisplay = mapColorToConflictType(statusColor)

                // Format shift times for display
                const formatShiftTime = (minutes: number | undefined) => {
                  if (!minutes && minutes !== 0) return '--:--'
                  const hours = Math.floor(minutes / 60)
                  const mins = minutes % 60
                  return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`
                }

                const shiftTimeDisplay = shiftWithChanges.start || shiftWithChanges.end
                  ? `${formatShiftTime(shiftWithChanges.start)} - ${formatShiftTime(shiftWithChanges.end)}`
                  : I18n.t('payroll.no_time_set')

                return (
                  <ShiftListItemStyled
                    key={index}
                    $isActive={activeShift === index}
                    $conflictType={conflictTypeForDisplay}
                    onClick={() => setActiveShift(index)}
                    title={shiftTimeDisplay} // Show time on hover
                  >
                    <ShiftTabContentStyled>
                      <span>{I18n.t('common.shift')} {index + 1}</span>
                    </ShiftTabContentStyled>
                  </ShiftListItemStyled>
                )
              })}

              {/* Show "+" tab if less than 4 shifts exist */}
              {localShifts.length < 4 && (
                <ShiftListItemStyled
                  $isActive={activeShift === localShifts.length}
                  onClick={async () => {
                    if (!onSave || !employee || !date) {
                      toast.error(I18n.t('payroll.unable_to_create_new_shift_missing_data'))
                      return
                    }

                    try {
                      // Create a new shift with default values
                      // const defaultStartTime = 9 * 60 // 9:00 AM
                      // const defaultEndTime = 17 * 60 // 5:00 PM

                      const newShift = {
                        start: undefined,
                        end: undefined,
                        breaks: {},
                        positionId: selectedRole || Object.keys(currentCompany?.jobs || {})[0] || '',
                        isConfirmed: false,
                        manuallyCreated: true
                      }

                      // Generate a new shift key
                      const newShiftKey = `shift_${Date.now()}`

                      // Save the new shift to Firebase
                      await onSave({ [newShiftKey]: newShift })

                      // The parent component will update the shifts array and re-render
                      // We don't need to set activeShift here as it will be handled by the parent
                      toast.success(I18n.t('payroll.new_shift_created_successfully'))

                    } catch (error) {
                      console.error('❌ Failed to create new shift:', error)
                      toast.error(I18n.t('payroll.failed_to_create_new_shift'))
                    }
                  }}
                >
                  +
                </ShiftListItemStyled>
              )}
            </ShiftListStyled>
            <RoleBlockStyled>
              <RoleBlockLabelStyled>
                {I18n.t('common.role')}
              </RoleBlockLabelStyled>
              <CustomSelectStyled
                options={options}
                value={options.find(option => option.value === selectedRole)}
                onChange={option => {
                  if (option) {
                    setSelectedRole(option.value as string)
                    // Update local state instead of immediately saving to Firebase
                    if (activeShift !== null) {
                      updateLocalShift(activeShift, { positionId: option.value as string })
                    }
                  }
                }}
                placeholder={I18n.t('payroll.select_role')}
                noOptionsMessage={I18n.t('payroll.no_roles_available')}
                components={{
                  IndicatorSeparator: null
                }}
                $noValue={!selectedRole}
              />
              <RoleBlockSalaryStyled>
                {/* Display rate based on type */}
                {type === 'hourly' && (
                  <div>{formattedSalary}{I18n.t('payroll.per_hour')}</div>
                )}
                {type === 'yearly' && (
                  <div>
                    <div>{formattedSalary}{I18n.t('payroll.per_year')}</div>
                    <YearlySalaryDetailsStyled>
                      ({(numericRate / 365).toLocaleString(isLocaleFr ? 'fr-FR' : 'en-US', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                      })}{I18n.t('payroll.per_day')})
                    </YearlySalaryDetailsStyled>
                  </div>
                )}

                {/* Show additional salary (bonus per shift) if available */}
                {hasBonus && (
                  <BonusSalaryStyled>
                    + {bonusAmount.toLocaleString(isLocaleFr ? 'fr-FR' : 'en-US', {
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2
                    })}
                  </BonusSalaryStyled>
                )}
              </RoleBlockSalaryStyled>
            </RoleBlockStyled>
            <DividerStyled />
            <ScrollBlockStyled>
              <ShiftBlockStyled>
                <RowStyled>
                  <RowCellStyled>{I18n.t('common.shift')}</RowCellStyled>
                  <RowCellStyled>{I18n.t('payroll.start')}</RowCellStyled>
                  <RowCellStyled>{I18n.t('common.end')}</RowCellStyled>
                  <RowCellStyled>{I18n.t('payroll.total')}</RowCellStyled>
                </RowStyled>
                <RowStyled>
                  <RowCellStyled>{I18n.t('payroll.planned')}</RowCellStyled>
                  <RowCellStyled>
                    {currentShift?.scheduledShift?.start
                      ? `${Math.floor(currentShift.scheduledShift.start / 60)}:${(currentShift.scheduledShift.start % 60).toString().padStart(2, '0')}`
                      : '--:--'}
                  </RowCellStyled>
                  <RowCellStyled>
                    {currentShift?.scheduledShift?.end
                      ? `${Math.floor(currentShift.scheduledShift.end / 60)}:${(currentShift.scheduledShift.end % 60).toString().padStart(2, '0')}`
                      : '--:--'}
                  </RowCellStyled>
                  <RowCellStyled>
                    {(hasWarning || (hoursTableAnalysis && hoursTableAnalysis.issues.length > 0)) && (
                      <WarningTooltipButton
                        isRed={hoursTableAnalysis?.status === 'overlapping'}
                        tooltipText={
                          hoursTableAnalysis?.issues.join(', ') ||
                          I18n.t('payroll.shift_not_planned_for_employee')
                        }
                        show={showPlannedTooltip}
                        canShow={canShowTooltip}
                        onToggleTooltip={() =>
                          setShowPlannedTooltip(!showPlannedTooltip)
                        }
                        onHide={() => setShowPlannedTooltip(false)}
                        buttonRef={plannedTooltipRef}
                      />
                    )}
                  </RowCellStyled>
                </RowStyled>
                <RowStyled>
                  <RowCellStyled>{I18n.t('payroll.clocked')}</RowCellStyled>
                  <RowCellStyled>
                    {(() => {
                      // Get actual clock-in time from attendance data
                      if (!attendanceData || !date || !employee) return '--:--'

                      const dayAttendance = attendanceData[date]
                      if (!dayAttendance) return '--:--'

                      const employeeAttendance = dayAttendance[employee.uid]
                      if (!employeeAttendance) return '--:--'

                      // Find the current shift's attendance data
                      const currentShiftKey = (currentShift as any)?.shiftKey
                      if (!currentShiftKey) return '--:--'

                      const attendanceShift = employeeAttendance[currentShiftKey]
                      if (!attendanceShift || !attendanceShift.start) return '--:--'

                      const clockInMinutes = attendanceShift.start
                      return `${Math.floor(clockInMinutes / 60)}:${(clockInMinutes % 60).toString().padStart(2, '0')}`
                    })()}
                  </RowCellStyled>
                  <RowCellStyled>
                    {(() => {
                      // Get actual clock-out time from attendance data
                      if (!attendanceData || !date || !employee) return '--:--'

                      const dayAttendance = attendanceData[date]
                      if (!dayAttendance) return '--:--'

                      const employeeAttendance = dayAttendance[employee.uid]
                      if (!employeeAttendance) return '--:--'

                      // Find the current shift's attendance data
                      const currentShiftKey = (currentShift as any)?.shiftKey
                      if (!currentShiftKey) return '--:--'

                      const attendanceShift = employeeAttendance[currentShiftKey]
                      if (!attendanceShift || !attendanceShift.end) return '--:--'

                      const clockOutMinutes = attendanceShift.end
                      return `${Math.floor(clockOutMinutes / 60)}:${(clockOutMinutes % 60).toString().padStart(2, '0')}`
                    })()}
                  </RowCellStyled>
                  <RowCellStyled>
                    {notClockedOut && (
                      <WarningTooltipButton
                        tooltipText={I18n.t(
                          'payroll.employee_did_not_clock_out'
                        )}
                        show={showClockedTooltip}
                        canShow={canShowTooltip}
                        onToggleTooltip={() =>
                          setShowClockedTooltip(!showClockedTooltip)
                        }
                        onHide={() => setShowClockedTooltip(false)}
                        buttonRef={clockedTooltipRef}
                      />
                    )}
                  </RowCellStyled>
                </RowStyled>
                <RowStyled>
                  <RowCellStyled>{I18n.t('payroll.rounded')}</RowCellStyled>
                  <CustomTimePickerStyled
                    minuteStep={1}
                    onChange={value => {
                      setRoundedTimeStart(value)
                      // Update local state only if value actually changed to prevent circular updates
                      if (activeShift !== null && value) {
                        const newStartMinutes = value.hour() * 60 + value.minute()
                        const currentStartMinutes = currentShift?.start
                        if (newStartMinutes !== currentStartMinutes) {
                          updateLocalShift(activeShift, {
                            start: newStartMinutes
                          })
                        }
                      }
                    }}
                    value={roundedTimeStart}
                    placeholder='-:-'
                    hideArrow
                    $noValue={!roundedTimeStart}
                    isFixedMenu
                  />
                  <CustomTimePickerStyled
                    minuteStep={1}
                    onChange={value => {
                      setRoundedTimeEnd(value)
                      // Update local state only if value actually changed to prevent circular updates
                      if (activeShift !== null && value) {
                        const newEndMinutes = value.hour() * 60 + value.minute()
                        const currentEndMinutes = currentShift?.end
                        if (newEndMinutes !== currentEndMinutes) {
                          updateLocalShift(activeShift, {
                            end: newEndMinutes
                          })
                        }
                      }
                    }}
                    value={roundedTimeEnd}
                    placeholder='-:-'
                    hideArrow
                    $noValue={!roundedTimeEnd}
                    isFixedMenu
                  />
                  <RowCellStyled>
                    {/* Removed short shift validation - managers can book any length shifts */}
                    {(() => {

                      return isOverlap && (
                        <WarningTooltipButton
                          isRed
                          tooltipText={
                            hoursTableAnalysis?.issues?.join(', ') ||
                            I18n.t('payroll.shift_overlaps_detected')
                          }
                          show={showOverlapTooltip}
                          canShow={canShowTooltip}
                          onToggleTooltip={() =>
                            setShowOverlapTooltip(!showOverlapTooltip)
                          }
                          onHide={() => setShowOverlapTooltip(false)}
                          buttonRef={overlapTooltipRef}
                        />
                      )
                    })()}
                    {(() => {
                      // Calculate actual hours based on start and end time
                      // Use currentShift from the logic above (handles both existing and new shifts)
                      if (!currentShift || !currentShift.start) return '0'

                      // Convert timestamps to minutes since midnight
                      const startMinutes = currentShift.start
                      let endMinutes = currentShift.end

                      // If no end time (not clocked out), use default 6.5 hours
                      if (!endMinutes) {
                        endMinutes = startMinutes + (6.5 * 60) // 6.5 hours in minutes
                      }

                      // Calculate total minutes worked
                      let totalMinutes = endMinutes - startMinutes

                      // Handle overnight shifts (end time is next day)
                      if (totalMinutes < 0) {
                        totalMinutes += 24 * 60 // Add 24 hours in minutes
                      }

                      // Subtract only unpaid break time
                      const breakMinutes = Object.values(currentShift.breaks || {}).reduce((total, breakItem) => {
                        // Only subtract unpaid breaks from total hours
                        const isUnpaid = (breakItem as any).isUnpaid || false
                        return total + (isUnpaid ? (breakItem.lengthRounded || 0) : 0)
                      }, 0)

                      const workMinutes = totalMinutes - breakMinutes
                      const workHours = workMinutes / 60

                      return Math.max(0, workHours).toFixed(1)
                    })()}{I18n.t('common.hours_shorten').toLowerCase()}
                    <DeleteButtonStyled
                      onClick={() => {
                        setShowDeleteShiftModal(true)
                        setShowClockedTooltip(false)
                        // Removed setShowShortShiftTooltip - shift length validation disabled
                        setShowOverlapTooltip(false)
                        setShowPlannedTooltip(false)
                        setCanShowTooltip( false )
                        handleDeleteShift()
                      }}
                    >
                      <DeleteIconStyled />
                    </DeleteButtonStyled>
                  </RowCellStyled>
                </RowStyled>
              </ShiftBlockStyled>
              <DividerStyled />

              <BreakBlockStyled>
                <RowStyled>
                  <RowCellStyled>{I18n.t('payroll.break')}</RowCellStyled>
                  <RowCellStyled>{I18n.t('payroll.start')}</RowCellStyled>
                  <RowCellStyled>{I18n.t('common.end')}</RowCellStyled>
                  <RowCellStyled>{I18n.t('payroll.total')}</RowCellStyled>
                </RowStyled>

                {/* Render multiple break rows dynamically */}
                {(() => {
                  const breaks = currentShift?.breaks || {}
                  const breakEntries = Object.entries(breaks)

                  // If no breaks exist, show one empty break row
                  if (breakEntries.length === 0) {
                    return (
                      <RowStyled key="empty-break">
                        <RowCellStyled>
                          {!isUnpaidBreak
                            ? I18n.t('payroll.paid')
                            : I18n.t('payroll.unpaid')}
                          <CustomCheckboxStyled
                            $isActive={!isUnpaidBreak}
                            onClick={() => {
                              setIsUnpaidBreak(!isUnpaidBreak)
                              // Update local state for the break paid/unpaid status
                              if (activeShift !== null && breakTimeStart && breakTimeEnd) {
                                const breakId = 'break1'
                                const startMinutes = breakTimeStart.hour() * 60 + breakTimeStart.minute()
                                const endMinutes = breakTimeEnd.hour() * 60 + breakTimeEnd.minute()

                                const updatedBreaks = {
                                  [breakId]: {
                                    start: startMinutes,
                                    end: endMinutes,
                                    lengthRounded: Math.max(0, endMinutes - startMinutes),
                                    isUnpaid: !isUnpaidBreak // This will be the new value since we toggle isUnpaidBreak above
                                  }
                                }
                                updateLocalShift(activeShift, { breaks: updatedBreaks })
                              }
                            }}
                          />
                        </RowCellStyled>
                        <CustomTimePickerStyled
                          minuteStep={1}
                          onChange={value => {
                            setBreakTimeStart(value)
                            // Update local state only if value actually changed to prevent circular updates
                            if (activeShift !== null && value && breakTimeEnd) {
                              const breakId = 'break1'
                              const startMinutes = value.hour() * 60 + value.minute()
                              const endMinutes = breakTimeEnd.hour() * 60 + breakTimeEnd.minute()

                              // Check if this break time actually changed
                              const currentBreakStart = currentShift?.breaks?.[breakId]?.start
                              if (startMinutes !== currentBreakStart) {
                                const updatedBreaks = {
                                  [breakId]: {
                                    start: startMinutes,
                                    end: endMinutes,
                                    lengthRounded: Math.max(0, endMinutes - startMinutes),
                                    isUnpaid: isUnpaidBreak
                                  }
                                }
                                updateLocalShift(activeShift, { breaks: updatedBreaks })
                              }
                            }
                          }}
                          value={breakTimeStart}
                          placeholder='-:-'
                          hideArrow
                          $noValue={!breakTimeStart}
                          isFixedMenu
                        />
                        <CustomTimePickerStyled
                          minuteStep={1}
                          onChange={value => {
                            setBreakTimeEnd(value)
                            // Update local state only if value actually changed to prevent circular updates
                            if (activeShift !== null && value && breakTimeStart) {
                              const breakId = 'break1'
                              const startMinutes = breakTimeStart.hour() * 60 + breakTimeStart.minute()
                              const endMinutes = value.hour() * 60 + value.minute()

                              // Check if this break time actually changed
                              const currentBreakEnd = currentShift?.breaks?.[breakId]?.end
                              if (endMinutes !== currentBreakEnd) {
                                const updatedBreaks = {
                                  [breakId]: {
                                    start: startMinutes,
                                    end: endMinutes,
                                    lengthRounded: Math.max(0, endMinutes - startMinutes),
                                    isUnpaid: isUnpaidBreak
                                  }
                                }
                                updateLocalShift(activeShift, { breaks: updatedBreaks })
                              }
                            }
                          }}
                          value={breakTimeEnd}
                          placeholder='-:-'
                          hideArrow
                          $noValue={!breakTimeEnd}
                          isFixedMenu
                        />
                        <RowCellStyled>
                          {(() => {
                            if (breakTimeStart && breakTimeEnd) {
                              const startMinutes = breakTimeStart.hour() * 60 + breakTimeStart.minute()
                              const endMinutes = breakTimeEnd.hour() * 60 + breakTimeEnd.minute()
                              return Math.max(0, endMinutes - startMinutes)
                            }
                            return 0
                          })()}
                          {I18n.t('common.minutes_shorten').toLowerCase()}
                          <DeleteButtonStyled
                            onClick={() => {
                              // Clear values only (don't delete since it's the only one)
                              setBreakTimeStart(null)
                              setBreakTimeEnd(null)
                              // Also clear from local state
                              if (activeShift !== null) {
                                updateLocalShift(activeShift, { breaks: {} })
                              }
                            }}
                          >
                            <DeleteIconStyled />
                          </DeleteButtonStyled>
                        </RowCellStyled>
                      </RowStyled>
                    )
                  }

                  // Render existing breaks
                  return breakEntries.map(([breakId, breakData]) => {
                    // Determine if this specific break is unpaid
                    const isThisBreakUnpaid = (breakData as any).isUnpaid || false

                    return (
                      <RowStyled key={breakId}>
                        <RowCellStyled>
                          {!isThisBreakUnpaid
                            ? I18n.t('payroll.paid')
                            : I18n.t('payroll.unpaid')}
                          <CustomCheckboxStyled
                            $isActive={!isThisBreakUnpaid}
                            onClick={() => {
                              if (activeShift !== null) {
                                const updatedBreaks = {
                                  ...currentShift?.breaks,
                                  [breakId]: {
                                    ...breakData,
                                    isUnpaid: !isThisBreakUnpaid
                                  }
                                }
                                updateLocalShift(activeShift, { breaks: updatedBreaks })
                              }
                            }}
                          />
                        </RowCellStyled>
                      <CustomTimePickerStyled
                        minuteStep={1}
                        onChange={value => {
                          if (activeShift !== null) {
                            const startMinutes = value ? value.hour() * 60 + value.minute() : 0
                            const updatedBreaks = {
                              ...currentShift?.breaks,
                              [breakId]: {
                                ...breakData,
                                start: startMinutes
                              }
                            }
                            updateLocalShift(activeShift, { breaks: updatedBreaks })
                          }
                        }}
                        value={breakData.start && breakData.start > 0 ? moment().hour(Math.floor(breakData.start / 60)).minute(breakData.start % 60) : null}
                        placeholder='-:-'
                        hideArrow
                        $noValue={!breakData.start || breakData.start === 0}
                        isFixedMenu
                      />
                      <CustomTimePickerStyled
                        minuteStep={1}
                        onChange={value => {
                          if (activeShift !== null) {
                            const endMinutes = value ? value.hour() * 60 + value.minute() : 0
                            const startMinutes = breakData.start || 0
                            const updatedBreaks = {
                              ...currentShift?.breaks,
                              [breakId]: {
                                ...breakData,
                                end: endMinutes,
                                lengthRounded: Math.max(0, endMinutes - startMinutes)
                              }
                            }
                            updateLocalShift(activeShift, { breaks: updatedBreaks })
                          }
                        }}
                        value={breakData.end && breakData.end > 0 ? moment().hour(Math.floor(breakData.end / 60)).minute(breakData.end % 60) : null}
                        placeholder='-:-'
                        hideArrow
                        $noValue={!breakData.end || breakData.end === 0}
                        isFixedMenu
                      />
                      <RowCellStyled>
                        {breakData.lengthRounded || 0}
                        {I18n.t('common.minutes_shorten').toLowerCase()}
                        <DeleteButtonStyled
                          onClick={(e) => {
                            console.log('Delete button clicked, showing tooltip for breakId:', breakId)
                            const buttonElement = e.currentTarget as HTMLButtonElement
                            setDeleteBreakButtonRef(buttonElement)
                            setDeleteBreakId(breakId)
                            setShowDeleteBreakTooltip(true)
                          }}
                        >
                          <DeleteIconStyled />
                        </DeleteButtonStyled>
                      </RowCellStyled>
                    </RowStyled>
                    )
                  })
                })()}

                <AddBreakButtonStyled
                  onClick={() => {
                    // Add a new break row using local state
                    if (activeShift !== null && currentShift) {
                      const newBreakId = `break_${Date.now()}`

                      const updatedBreaks = {
                        ...currentShift.breaks,
                        [newBreakId]: {
                          start: 0,
                          end: 0,
                          lengthRounded: 0
                        }
                      }

                      updateLocalShift(activeShift, { breaks: updatedBreaks })
                    }
                  }}
                >
                  {I18n.t('payroll.add_break')} <PlusIconStyled />
                </AddBreakButtonStyled>
              </BreakBlockStyled>
            </ScrollBlockStyled>

            <DividerStyled />

            <TotalBlockStyled>
              <TotalBlockLabelStyled>
                {I18n.t('payroll.total')}
              </TotalBlockLabelStyled>
              <TotalBlockValueStyled>
                {(() => {
                  // Calculate total hours including current shift being edited
                  let totalHours = 0

                  // Add hours from existing shifts
                  localShifts.forEach((shift, index) => {
                    // Skip the current shift if we're editing it
                    if (index === activeShift) return

                    // Apply any local changes to this shift
                    const shiftWithChanges = unsavedChanges[index] ? { ...shift, ...unsavedChanges[index] } : shift

                    if (!shiftWithChanges.start || !shiftWithChanges.end) return

                    let shiftMinutes = shiftWithChanges.end - shiftWithChanges.start
                    if (shiftMinutes < 0) shiftMinutes += 24 * 60
                    const breakMinutes = Object.values(shiftWithChanges.breaks || {}).reduce((breakTotal, breakItem) => {
                      // Only subtract unpaid breaks from total hours
                      const isUnpaid = (breakItem as any).isUnpaid || false
                      return breakTotal + (isUnpaid ? (breakItem.lengthRounded || 0) : 0)
                    }, 0)
                   // Subtract only unpaid break time

                    const workMinutes = shiftMinutes - breakMinutes
                    totalHours += workMinutes / 60
                  })

                  // Add hours from current shift being edited
                  if (currentShift && currentShift.start) {
                    const startMinutes = currentShift.start
                    let endMinutes = currentShift.end || (startMinutes + 6.5 * 60) // Default 6.5 hours

                    let shiftMinutes = endMinutes - startMinutes
                    if (shiftMinutes < 0) shiftMinutes += 24 * 60

                    // Calculate break minutes from current shift data (includes local changes)
                    const breakMinutes = Object.values(currentShift.breaks || {}).reduce((breakTotal, breakItem) => {
                      // Only subtract unpaid breaks from total hours
                      const isUnpaid = (breakItem as any).isUnpaid || false
                      return breakTotal + (isUnpaid ? (breakItem.lengthRounded || 0) : 0)
                    }, 0)

                    const workMinutes = shiftMinutes - breakMinutes
                    totalHours += workMinutes / 60
                  }

                  return Math.max(0, totalHours).toFixed(2)
                })()}{I18n.t('common.hours_shorten').toLowerCase()}
              </TotalBlockValueStyled>
              <TotalBlockValueStyled>
                <NumberFormatted value={(() => {
                  // Calculate total pay based on hours and rate type
                  const totalHours = shifts.reduce((total, shift) => {
                    if (!shift.start || !shift.end) return total

                    let shiftMinutes = shift.end - shift.start
                    if (shiftMinutes < 0) shiftMinutes += 24 * 60

                    const breakMinutes = Object.values(shift.breaks || {}).reduce((breakTotal, breakItem) => {
                      // Only subtract unpaid breaks from total hours for pay calculation
                      const isUnpaid = (breakItem as any).isUnpaid || false
                      return breakTotal + (isUnpaid ? (breakItem.lengthRounded || 0) : 0)
                    }, 0)

                    const workMinutes = shiftMinutes - breakMinutes
                    return total + (workMinutes / 60)
                  }, 0)

                  const numericRate = Number(rate) || 0

                  if (type === 'hourly') {
                    // For hourly employees: hours * hourly rate
                    return Math.max(0, totalHours * numericRate)
                  } else {
                    // For yearly salary employees: yearly salary / 365 (daily rate)
                    return Math.max(0, numericRate / 365)
                  }
                })()} />
              </TotalBlockValueStyled>
            </TotalBlockStyled>

            <ButtonBlockStyled>
              {hoursTableAnalysis && (() => {
                const buttonStates = getButtonStatesFromShiftAnalysis(hoursTableAnalysis)

                return (
                  <>
                    <ButtonWrapStyled>
                      {buttonStates.showReclaim && (
                        <OverlayTrigger
                          trigger={['hover', 'focus']}
                          placement='top-start'
                          overlay={
                            <TooltipStyled
                              id='shift-popover_reclaim-tooltip'
                              $isReclaimTooltip
                            >
                              {I18n.t('payroll.you_save')} ${saveAmount}{' '}
                              {I18n.t('payroll.with_planned_start_time')}
                            </TooltipStyled>
                          }
                        >
                          <OrangeButtonStyled
                            color='orange'
                            onClick={() => {
                              onChangeStatus('claim', () =>
                                setHasClaimed(!hasClaimed)
                              )
                              setHasClaimed(!hasClaimed)
                              setHasUnsavedChanges(true)
                            }}
                            $isActive={hasClaimed}
                          >
                            <p>
                              {I18n.t('payroll.reclaim')}{' '}
                              <NumberFormatted value={saveAmount} />
                            </p>
                            <p>
                              {I18n.t('payroll.saved')}{' '}
                              <NumberFormatted value={saveAmount} />
                            </p>
                          </OrangeButtonStyled>
                        </OverlayTrigger>
                      )}
                    </ButtonWrapStyled>
                    <ButtonWrapStyled>
                      {/* Always show approve button - remove blocking conditions */}
                      <SaveButtonStyled
                        color={buttonStates.approveButtonColor || 'green'}
                        onClick={async () => {
                          if (hasClaimed) {
                            setHasClaimed(!hasClaimed)
                          } else {
                            try {
                              await saveLocalChanges() // Save any unsaved changes first
                              onChangeStatus('approve')
                              onClose()
                            } catch (error) {
                              // Error is already handled in saveLocalChanges
                            }
                          }
                        }}
                        $isActive={hasClaimed}
                        $conflictColor={mapColorToConflictType(getShiftStatusColor(hoursTableAnalysis.status))}
                      >
                        <span>{I18n.t('common.approve')}</span>
                        <span>{I18n.t('common.undo')}</span>
                      </SaveButtonStyled>
                    </ButtonWrapStyled>
                  </>
                )
              })()}

              {/* Fallback for when analysis is not available */}
              {!hoursTableAnalysis && (
                <>
                  <ButtonWrapStyled>
                    {canReclaim && (
                      <OverlayTrigger
                        trigger={['hover', 'focus']}
                        placement='top-start'
                        overlay={
                          <TooltipStyled
                            id='shift-popover_reclaim-tooltip'
                            $isReclaimTooltip
                          >
                            {I18n.t('payroll.you_save')} ${saveAmount}{' '}
                            {I18n.t('payroll.with_planned_start_time')}
                          </TooltipStyled>
                        }
                      >
                        <OrangeButtonStyled
                          color='orange'
                          onClick={ () =>
                          {
                            if ( hasClaimed )
                            {
                              setHasClaimed( !hasClaimed );
                            } else
                            {
                              onChangeStatus( 'approve' );
                              onClose();
                            }
                          } }
                          $isActive={hasClaimed}
                        >
                          <p>
                            {I18n.t('payroll.reclaim')}{' '}
                            <NumberFormatted value={saveAmount} />
                          </p>
                          <p>
                            {I18n.t('payroll.saved')}{' '}
                            <NumberFormatted value={saveAmount} />
                          </p>
                        </OrangeButtonStyled>
                      </OverlayTrigger>
                    )}
                  </ButtonWrapStyled>
                  <ButtonWrapStyled>
                    {canReclaim ? (
                      <SaveButtonStyled
                        color='green'
                        onClick={async () => {
                          if (hasClaimed) {
                            setHasClaimed(!hasClaimed)
                          } else {
                            try {
                              await saveLocalChanges() // Save any unsaved changes first
                              onChangeStatus('approve')
                              onClose()
                            } catch (error) {
                              // Error is already handled in saveLocalChanges
                            }
                          }
                        }}
                        $isActive={hasClaimed}
                      >
                        <span>{I18n.t('common.approve')}</span>
                        <span>{I18n.t('common.undo')}</span>
                      </SaveButtonStyled>
                    ) : (
                      <GreyButtonStyled onClick={async () => {
                        try {
                          await saveLocalChanges() // Save local changes instead of using handleSaveShift
                          toast.success(I18n.t('payroll.shift_saved_successfully'))
                          onClose()
                        } catch (error) {
                          toast.error(I18n.t('payroll.failed_to_save_shift'))
                        }
                      }}>
                        {I18n.t('common.save')}
                      </GreyButtonStyled>
                    )}
                  </ButtonWrapStyled>
                </>
              )}
            </ButtonBlockStyled>
          </ContainerStyled>
        </PopoverStyled>
        <Overlay
          rootClose
          show={showActivityPopover}
          placement={showActivityPopoverOnLeft ? 'left-start' : 'right-start'}
          target={() => activityPopoverRef.current}
          onHide={() => setShowActivityPopover(false)}
        >
          <ActivitiesPopover
            onClose={() => setShowActivityPopover(false)}
            employee={employee}
            date={date}
            currentCompany={currentCompany}
          />
        </Overlay>
        <Overlay
          show={showDeleteBreakTooltip}
          placement='top'
          target={() => deleteBreakButtonRef}
          onHide={() => {
            setShowDeleteBreakTooltip(false)
            setDeleteBreakId(null)
            setDeleteBreakButtonRef(null)
          }}
        >
          <TooltipStyled
            onClick={(e: React.MouseEvent<HTMLDivElement>) => e.stopPropagation()}
            id='payroll_shift-popover-delete-break-tooltip'
          >
            {I18n.t('payroll.are_you_sure_delete_break')}
            <DeleteTooltipButtonStyled
              onClick={() => {
                console.log('Delete confirmed, executing delete function for breakId:', deleteBreakId)
                if (activeShift !== null && currentShift && deleteBreakId) {
                  const breakCount = Object.keys(currentShift.breaks || {}).length

                  if (breakCount > 1) {
                    // Delete this specific break if more than one exists
                    const updatedBreaks = { ...currentShift.breaks }
                    delete updatedBreaks[deleteBreakId]
                    updateLocalShift(activeShift, { breaks: updatedBreaks })
                  } else {
                    // Clear values only if it's the last break
                    const updatedBreaks = {
                      [deleteBreakId]: {
                        start: 0,
                        end: 0,
                        lengthRounded: 0
                      }
                    }
                    updateLocalShift(activeShift, { breaks: updatedBreaks })
                  }
                }
                setShowDeleteBreakTooltip(false)
                setDeleteBreakId(null)
                setDeleteBreakButtonRef(null)
              }}
            >
              {I18n.t('common.delete')}
            </DeleteTooltipButtonStyled>
            <CloseButtonTooltipStyled onClick={() => {
              setShowDeleteBreakTooltip(false)
              setDeleteBreakId(null)
            }}>
              <CloseIconStyled />
            </CloseButtonTooltipStyled>
          </TooltipStyled>
        </Overlay>
      </>
    )
  }
)

type WarningTooltipButtonProps = {
  isRed?: boolean
  tooltipText: string
  show: boolean
  canShow: boolean
  onToggleTooltip: () => void
  onHide: () => void
  buttonRef: React.RefObject<HTMLButtonElement>
}

const WarningTooltipButton = ({
  isRed,
  tooltipText,
  show,
  canShow,
  onToggleTooltip,
  onHide,
  buttonRef
}: WarningTooltipButtonProps) => (
  <>
    <WarningButtonStyled
      $isRed={isRed}
      onClick={() => canShow && onToggleTooltip()}
      ref={buttonRef}
    >
      <WarningIconStyled />
    </WarningButtonStyled>
    <Overlay
      show={show && canShow}
      placement='top'
      target={() => buttonRef.current}
      onHide={onHide}
    >
      <TooltipStyled
        $isRed={isRed}
        onClick={(e: React.MouseEvent<HTMLDivElement>) => e.stopPropagation()}
        id='payroll_shift-popover-tooltip'
      >
        {tooltipText}
        <CloseButtonTooltipStyled onClick={onHide}>
          <CloseIconStyled />
        </CloseButtonTooltipStyled>
      </TooltipStyled>
    </Overlay>
  </>
)

export default ShiftPopover
